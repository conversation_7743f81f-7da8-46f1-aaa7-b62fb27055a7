"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Share2, Twitter, Facebook, Linkedin, Copy } from "lucide-react"
import type { Post } from "@/types/blog"
import { useState } from "react"
import { toast } from "sonner"

interface ShareButtonsProps {
  post: Post
}

export default function ShareButtons({ post }:any) {
  const [isSharing, setIsSharing] = useState(false)

  const url = typeof window !== "undefined" ? window.location.href : ""
  const title = post.title
  const text = post.excerpt

  const shareUrls = {
    twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(title)}&url=${encodeURIComponent(url)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`,
    linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`,
  }

  const handleShare = async (platform?: string) => {
    if (platform) {
      window.open(shareUrls[platform as keyof typeof shareUrls], "_blank", "width=600,height=400")
      return
    }

    // Native Web Share API
    if (navigator.share) {
      try {
        await navigator.share({
          title,
          text,
          url,
        })
      } catch (error) {
        console.log("Share cancelled")
      }
    } else {
      // Fallback: copy to clipboard
      try {
        await navigator.clipboard.writeText(url)
        toast.success("Link copied to clipboard!")
      } catch (error) {
        toast.error("Failed to copy link")
      }
    }
  }

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(url)
      toast.success("Link copied to clipboard!")
    } catch (error) {
      toast.error("Failed to copy link")
    }
  }

  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground mr-2">Share:</span>

      {/* Native share button (mobile) */}
      {typeof window !== "undefined" && typeof navigator.share === "function" && (
        <Button variant="outline" size="sm" onClick={() => handleShare()} className="md:hidden">
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      )}

      {/* Individual platform buttons (desktop) */}
      <div className="hidden md:flex items-center gap-2">
        <Button variant="outline" size="sm" onClick={() => handleShare("twitter")}>
          <Twitter className="h-4 w-4" />
          <span className="sr-only">Share on Twitter</span>
        </Button>

        <Button variant="outline" size="sm" onClick={() => handleShare("facebook")}>
          <Facebook className="h-4 w-4" />
          <span className="sr-only">Share on Facebook</span>
        </Button>

        <Button variant="outline" size="sm" onClick={() => handleShare("linkedin")}>
          <Linkedin className="h-4 w-4" />
          <span className="sr-only">Share on LinkedIn</span>
        </Button>

        <Button variant="outline" size="sm" onClick={copyToClipboard}>
          <Copy className="h-4 w-4" />
          <span className="sr-only">Copy link</span>
        </Button>
      </div>
    </div>
  )
}
