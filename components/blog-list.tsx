import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { getPostsPaginated, searchPosts, SearchResult } from "@/lib/blog-data"
import { Calendar, Clock, ArrowRight, User } from "lucide-react"
import Pagination from "@/components/pagination"

interface BlogListProps {
  page: number
  searchQuery?: string
}

type Post = {
  id: string | number
  title: string
  slug: string
  excerpt: string
  coverImage?: string
  featured?: boolean
  publishedAt?: string
  readingTime?: number
  category?: string | { name: string }
  tags: Array<string | { tag: { name: string } }>
  author?: { name: string }
}

export default async function BlogList({ page, searchQuery }: BlogListProps) {
  let posts: Post[], pagination

  if (searchQuery) {
    posts = await searchPosts(searchQuery)

    pagination = {
      currentPage: 1,
      totalPages: 1,
      totalPosts: posts.length,
      hasNext: false,
      hasPrev: false,
    }
  } else {
    const result = await getPostsPaginated(page, 6)
    posts = result.posts
    pagination = result.pagination
  }

  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-semibold mb-4">No articles found</h2>
        <p className="text-muted-foreground mb-6">
          {searchQuery ? `No results for "${searchQuery}"` : "No articles available at the moment."}
        </p>
        {searchQuery && (
          <Button asChild variant="outline">
            <Link href="/blog">View All Articles</Link>
          </Button>
        )}
      </div>
    )
  }

  return (
    <div>
      {searchQuery && (
        <div className="mb-8">
          <p className="text-muted-foreground">
            Found {posts.length} result{posts.length !== 1 ? "s" : ""} for "{searchQuery}"
          </p>
        </div>
      )}

      <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
        {posts.map((post) => (
          <Card
            key={post.id}
            className="group hover:shadow-xl transition-all duration-300 overflow-hidden h-full flex flex-col"
          >
            <div className="relative h-48 overflow-hidden">
              <Image
                src={post.coverImage || "/placeholder.svg?height=200&width=400"}
                alt={post.title}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
              {post.featured && (
                <div className="absolute top-4 left-4">
                  <Badge className="bg-yellow-500 text-black">Featured</Badge>
                </div>
              )}
            </div>

            <CardHeader className="pb-2 flex-grow">
              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {post.publishedAt ? new Date(post.publishedAt).toLocaleDateString() : ''}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {post.readingTime} min
                </div>
              </div>

              <Badge variant="outline" className="w-fit mb-2">
                {typeof post.category === "string"
                  ? post.category
                  : post.category?.name || "Uncategorized"}
              </Badge>

              <h2 className="text-xl font-semibold group-hover:text-blue-600 transition-colors line-clamp-2">
                <Link href={`/blog/${post.slug}`}>{post.title}</Link>
              </h2>
            </CardHeader>

            <CardContent className="pt-0">
              <p className="text-muted-foreground mb-4 line-clamp-3">{post.excerpt}</p>

              <div className="flex flex-wrap gap-2 mb-4">
                {post.tags.slice(0, 3).map((tag: any) => {
                  if (typeof tag === "string") {
                    return (
                      <Badge key={tag} variant="secondary" className="text-xs">
                        <Link href={`/tags/${tag.toLowerCase()}`}>{tag}</Link>
                      </Badge>
                    )
                  } else {
                    return (
                      <Badge key={tag.tag.name} variant="secondary" className="text-xs">
                        <Link href={`/tags/${tag.tag.name.toLowerCase()}`}>{tag.tag.name}</Link>
                      </Badge>
                    )
                  }
                })}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <User className="h-4 w-4" />
                  {post.author?.name}
                </div>

                <Button asChild variant="ghost" size="sm" className="font-semibold text-blue-600">
                  <Link href={`/blog/${post.slug}`}>
                    Read More <ArrowRight className="ml-1 h-4 w-4" />
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {!searchQuery && pagination.totalPages > 1 && <Pagination pagination={pagination} basePath="/blog" />}
    </div>
  )
}
