import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "sonner"
import Navigation from "@/components/navigation"
import Footer from "@/components/footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "cline ai - AI Agent Programming & Automation",
    template: "%s | cline ai",
  },
  description:
    "Explore cline ai, an advanced platform for AI agent programming, automation, and intelligent code generation. Learn about building, deploying, and managing AI-powered agents for modern software development.",
  keywords: [
    "cline ai",
    "AI agent",
    "AI programming",
    "automation",
    "code generation",
    "machine learning",
    "software development",
    "Next.js",
    "React",
    "intelligent agents"
  ],
  authors: [{ name: "cline ai Team" }],
  creator: "cline ai",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://clinecoder.com",
    siteName: "clinecoder",
    title: "cline ai | AI Agent Programming & Automation",
    description:
      "Discover cline ai, a platform focused on AI agent programming, automation, and intelligent code solutions for developers.",
  },
  twitter: {
    card: "summary_large_image",
    title: "cline ai - AI Agent Programming & Automation",
    description:
      "Explore cline ai for the latest in AI agent programming, automation, and intelligent code generation.",
    creator: "@clinecoder",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
  },
  generator: "clinecoder",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
          <div className="min-h-screen flex flex-col">
            <Navigation />
            <main className="flex-1">{children}</main>
            <Footer />
          </div>
          <Toaster />
        </ThemeProvider>
      </body>
    </html>
  )
}
