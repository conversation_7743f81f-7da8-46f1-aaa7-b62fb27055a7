import type { <PERSON><PERSON><PERSON> } from "next"
import { notFound } from "next/navigation"
import { Suspense } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { getPostsByTag, getAllTags, getPostsByCategory } from "@/lib/blog-data"
import { ArrowLeft, Tag } from "lucide-react"
import BlogListSkeleton from "@/components/blog-list-skeleton"
import prisma  from "@/lib/prisma"

interface CategoryPageProps {
  params: Promise<{ slug: string }>
  searchParams: Promise<{ page?: string }>
}

let category: any;


export async function generateMetadata({ params}: CategoryPageProps): Promise<Metadata> {
  const { slug } = await params

  // Check if category exists
  category = await prisma.category.findUnique({
    where: { slug }
  })
  if (!category) {
    return {
      title: "Category Not Found",
      description: "The requested category does not exist.",
      openGraph: {
        title: "Category Not Found",
        description: "The requested category does not exist.",
        type: "website",  
      },
    }
  }

  return {
    title: `${category?.name} Articles - clinecoder`,
    description: `Browse all articles in the "${category?.name}" category.`,
    openGraph: {
      title: `${category?.name} Articles - clinecoder`,
      description: `Browse all articles in the "${category?.name}" category.`,
      type: "website",
    },  
  }
}

export default async function CategoryPage({ params, searchParams }: CategoryPageProps) {
  const { slug } = await params
  const { page } = await searchParams

  const currentPage = Number.parseInt(page || "1")
 

  const { posts, pagination } = await getPostsByCategory(slug, currentPage, 10)
  console.log("Posts:", posts)
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      <div className="max-w-7xl mx-auto px-4 py-12">
        {/* Back Button */}
        <div className="mb-8">
          <Button asChild variant="ghost" size="sm">
            <Link href="/categories">
              <ArrowLeft className="h-4 w-4 mr-2" />
              All Categories
            </Link>
          </Button>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Tag className="h-8 w-8 text-blue-600" />
            <Badge variant="outline" className="text-lg px-4 py-2">
              {category?.name}
            </Badge>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold mb-4">Articles in "{category?.name}" Category</h1>
          <p className="text-lg text-muted-foreground">
            Found {pagination.totalPosts} article{pagination.totalPosts !== 1 ? "s" : ""} in this category
          </p>
        </div>

        {/* Posts */}
        <Suspense fallback={<BlogListSkeleton />}>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            {posts.map((post) => (
              <div key={post.id} className="group">
                <Link href={`/blog/${post.slug}`} className="block">
                  <div className="bg-white dark:bg-slate-800 rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden h-full">
                    <div className="relative h-48 overflow-hidden">
                      <img
                        src={post.coverImage || "/placeholder.svg?height=200&width=400"}
                        alt={post.title}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      {post.featured && (
                        <div className="absolute top-4 left-4">
                          <Badge className="bg-yellow-500 text-black">Featured</Badge>
                        </div>
                      )}
                    </div>

                    <div className="p-6">
                      <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                        <span>{post.publishedAt}</span>
                        <span>{post.readingTime} min read</span>
                      </div>

                      <Badge variant="outline" className="mb-3">
                        {post.category.name}
                      </Badge>

                      <h2 className="text-xl font-semibold mb-3 group-hover:text-blue-600 transition-colors line-clamp-2">
                        {post.title}
                      </h2>

                      <p className="text-muted-foreground mb-4 line-clamp-3">{post.excerpt}</p>

                      <div className="flex flex-wrap gap-2">
                        {post.tags.slice(0, 3).map((postTag: any) => (
                          <Badge
                            key={postTag.id}
                            variant={"default"}
                            className="text-xs"
                          >
                            {postTag.tag.name}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  </div>
                </Link>
              </div>
            ))}
          </div>
        </Suspense>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-center gap-2">
            {pagination.hasPrev && (
              <Button asChild variant="outline">
                <Link href={`/category/${slug}?page=${pagination.currentPage - 1}`}>Previous</Link>
              </Button>
            )}

            <span className="px-4 py-2 text-sm text-muted-foreground">
              Page {pagination.currentPage} of {pagination.totalPages}
            </span>

            {pagination.hasNext && (
              <Button asChild variant="outline">
                <Link href={`/category/${slug}?page=${pagination.currentPage + 1}`}>Next</Link>
              </Button>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
