import type { <PERSON><PERSON><PERSON> } from "next"
import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { getFeaturedPosts, getAllTags, getAllCategories } from "@/lib/blog-data"
import { Calendar, Clock, ArrowRight, Tag, Folder } from "lucide-react"
import { type PostTag } from "@prisma/client"

export const metadata: Metadata = {
  title: "Modern Blog - Latest Articles on Web Development",
  description:
    "Discover the latest articles on web development, JavaScript, React, Next.js, and modern programming techniques.",
  keywords: ["blog", "web development", "JavaScript", "React", "Next.js", "programming"],
  openGraph: {
    title: "Modern Blog - Latest Articles on Web Development",
    description:
      "Discover the latest articles on web development, JavaScript, React, Next.js, and modern programming techniques.",
    type: "website",
    url: "https://yourblog.com",
  },
  twitter: {
    card: "summary_large_image",
    title: "Modern Blog - Latest Articles on Web Development",
    description:
      "Discover the latest articles on web development, JavaScript, React, Next.js, and modern programming techniques.",
  },
}

export default async function HomePage() {
  const featuredPosts = await getFeaturedPosts()
  const tags = (await getAllTags()).slice(0, 10)
  console.log("Tags:", tags)
  const categories = await getAllCategories()

  return (
    <div className="min-h-screen container mx-auto bg-gradient-to-br from-slate-50 to-slate-100 dark:from-slate-900 dark:to-slate-800">
      {/* Hero Section */} 
      <section className="relative py-20 px-4 text-center">
        <div className=" mx-auto">
          <h1 className="text-5xl md:text-7xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">
            cline ai
          </h1>
          <p className="text-xl md:text-2xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Discover the latest insights in web development, programming, and technology
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="text-lg px-8">
              <Link href="/blog">
                Explore Articles <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild variant="outline" size="lg" className="text-lg px-8">
              <Link href="/tags">Browse Topics</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-16 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Featured Articles</h2>
            <p className="text-lg text-muted-foreground">Our most popular and recent posts</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredPosts.map((post) => (
              <Card key={post.id} className="group hover:shadow-xl transition-all duration-300 overflow-hidden">
                <div className="relative h-48 overflow-hidden">
                  <Image
                    src={post.coverImage || "/placeholder.svg?height=200&width=400"}
                    alt={post.title}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge variant="secondary" className="bg-white/90 text-black">
                      Featured
                    </Badge>
                  </div>
                </div>
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-4 text-sm text-muted-foreground mb-2">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      {new Date(post.publishedAt).toLocaleDateString()}
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      {post.readingTime} min read
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold group-hover:text-blue-600 transition-colors">
                    <Link href={`/blog/${post.slug}`}>{post.title}</Link>
                  </h3>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground mb-4 line-clamp-3">{post.excerpt}</p>
                  <div className="flex flex-wrap gap-2 mb-4">
                    {post.tags.slice(0, 3).map((tag: string) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                  <Button asChild variant="ghost" className="p-0 h-auto font-semibold text-blue-600">
                    <Link href={`/blog/${post.slug}`}>
                      Read More <ArrowRight className="ml-1 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Tags and Categories */}
      <section className="py-16 px-4 bg-white/50 dark:bg-slate-800/50">
        <div className="max-w-7xl mx-auto">
          <div className="grid md:grid-cols-2 gap-12">
            {/* Popular Tags */}
            <div>
              <div className="flex items-center gap-2 mb-6">
                <Tag className="h-6 w-6 text-blue-600" />
                <h3 className="text-2xl font-bold">Popular Tags</h3>
              </div>
              <div className="flex flex-wrap gap-3">
                {tags.map((tag) => (
                  <Badge
                    key={tag.name}
                    variant="secondary"
                    className="text-sm py-2 px-4 hover:bg-blue-100 hover:text-blue-800 transition-colors cursor-pointer"
                  >
                    <Link href={`/tags/${tag.name.toLowerCase()}`}>
                      {tag.name} ({tag._count?.posts})
                    </Link>
                  </Badge>
                ))}
              </div>
            </div>

            {/* Categories */}
            <div>
              <div className="flex items-center gap-2 mb-6">
                <Folder className="h-6 w-6 text-purple-600" />
                <h3 className="text-2xl font-bold">Categories</h3>
              </div>
              <div className="space-y-3">
                {categories.map((category) => (
                  <div
                    key={category.name}
                    className="flex items-center justify-between p-3 rounded-lg bg-white dark:bg-slate-700 hover:shadow-md transition-shadow"
                  >
                    <Link
                      href={`/category/${category.slug}`}
                      className="font-medium hover:text-blue-600 transition-colors"
                    >
                      {category.name}
                    </Link>
                    <Badge variant="outline">{category._count?.posts}</Badge>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 text-center">
        <div className="max-w-3xl mx-auto">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">Ready to Dive Deeper?</h2>
          <p className="text-lg text-muted-foreground mb-8">
            Explore our complete collection of articles and stay updated with the latest in web development.
          </p>
          <Button asChild size="lg" className="text-lg px-8">
            <Link href="/blog">
              View All Articles <ArrowRight className="ml-2 h-5 w-5" />
            </Link>
          </Button>
        </div>
      </section>
    </div>
  )
}
