import type { Post, Tag, Category, PaginationInfo } from "@/types/blog"
import prisma from "@/lib/prisma"
import { Prisma } from "@prisma/client"


export async function getAllPosts(): Promise<Post[]> {
  const posts = await prisma.post.findMany({
    where: { published: true },
    include: {
      tags: {
        include:{
          tag: true
        }
      },
      category: true,
    },
    orderBy: { publishedAt: 'desc' },
  })
  return posts.map((post: any) => ({
    ...post,
    author: post.author || "Unknown Author",
    publishedAt: post.publishedAt ? post.publishedAt.toISOString() : new Date().toISOString(),
    updatedAt: post.updatedAt.toISOString(),
    readingTime: Math.ceil(post.content.split(' ').length / 200),
    tags: post.tags.map((tag: any) => tag),
  }));

}

export async function getPostsPaginated(page = 1, limit = 6): Promise<{ posts: Post[]; pagination: PaginationInfo }> {
  const posts = await prisma.post.findMany({
    where: { published: true },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      category: true,
    },
    take: limit,
    skip: (page - 1) * limit,
    orderBy: { publishedAt: 'desc' },
  })
  if (!posts || posts.length === 0) {
    return {
      posts: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalPosts: 0,
        hasNext: false,
        hasPrev: false,
      },
    }
  }
  const totalPages = await prisma.post.count({
    where: { published: true },
  }) / limit
  const allPosts = posts.map((post: any) => ({
    ...post,
    publishedAt: post.publishedAt.toISOString(),
    updatedAt: post.updatedAt.toISOString(),
    readingTime: Math.ceil(post.content.split(' ').length / 200), // Assuming average reading speed of 200 wpm
  }))
  

  return {
    posts:allPosts,
    pagination: {
      currentPage: page,
      totalPages,
      totalPosts: allPosts.length,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  }
}

export async function getPostBySlug(slug: string){
  const post = await prisma.post.findUnique({
    where: { slug },
    include: {
      tags: {
        include: {
          tag: true
        }
      },
      category: true,
      author: true,
    },
  })
  if (!post) return null
  return {
    ...post,
    publishedAt: post.publishedAt ? post.publishedAt.toISOString() : new Date().toISOString(),
    updatedAt: new Date(post.updatedAt).toISOString(),
    readingTime: Math.ceil(post.content.split(' ').length / 200), // Assuming average reading speed of 200 wpm
    coverImage: post.coverImage ? post.coverImage : null, // Ensure coverImage is null if not set
    author: post.author.name || "Unknown Author", // Fallback for author if not set
    excerpt: post.excerpt || post.content.slice(0, 150) + '...', // Fallback for excerpt if not set

    category: post.category || { name: "Uncategorized", slug: "uncategorized" }, // Fallback for category if not set
  }
}

export async function getPostsByTag(tagSlug: string, page = 1, limit = 6){
  const posts = await prisma.post.findMany({
    where: {
      published: true,
      tags: {
        some: {
          tag: {
            name: {
 equals: tagSlug,
              mode: 'insensitive', // Case-insensitive match
            },
          },
        },
      },
    },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      category: true,
    },
    skip: (page - 1) * limit,
    take: limit,
    orderBy: { publishedAt: 'desc' },
  })
  if (!posts || posts.length === 0) {
    return {
      posts: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalPosts: 0,
        hasNext: false,
        hasPrev: false,
      },
    }
  }
  const allPosts = posts.map((post: any) => ({
    ...post,
    publishedAt: post.publishedAt.toISOString(),
    updatedAt: post.updatedAt.toISOString(),
    readingTime: Math.ceil(post.content.split(' ').length / 200), // Assuming average reading speed of 200 wpm
  }))

 const totalPages = Math.ceil(await prisma.post.count({
    where: {
      published: true,
      tags: {
        some: {
          tag: {
            name: {
 equals: tagSlug,
              mode: 'insensitive', // Case-insensitive match
            },
          },
        },
      },
    },
  }) / limit)
  if (totalPages === 0) {
    return {
      posts: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalPosts: 0,
        hasNext: false,
        hasPrev: false,
      },
    }
  }

  return {
    posts,
    pagination: {
      currentPage: page,
      totalPages,
      totalPosts: allPosts.length,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  }
}

export async function getAllTags() {
  const tagWithCount = await prisma.tag.findMany({
    include: {
      _count: {
        select: {
          posts: true,
        },
      },
    }
  })
  console.log(tagWithCount)

  return tagWithCount
}

export async function getAllCategories(){
  const cateWithCount = await prisma.category.findMany({
    include: {
      _count: {
        select: {
          posts: true,
        },
      },
    }
  })
  console.log(cateWithCount)

  return cateWithCount
}

export async function getFeaturedPosts(): Promise<Post[]> {
  const posts = await prisma.post.findMany({
    where: { published: true, featured: true },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      category: true,
      author: true,
    },
    orderBy: { publishedAt: 'desc' },
    take: 6,
  })
  if (!posts || posts.length === 0) {
    return []
  }
  return posts.map((post: any) => ({
    ...post,
    publishedAt: post.publishedAt.toISOString(),
    updatedAt: post.updatedAt.toISOString(),
    readingTime: Math.ceil(post.content.split(' ').length / 200),
    author: post.author.name,
    tags: post.tags.map((tag: any) => tag.tag.name),
    category: post.category.name
  }))
}

export async function getRelatedPosts(categoryId: string, limit = 3) {
  const posts = await prisma.post.findMany({
    where: {
      published: true,
      categoryId,
    },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      category: true,
      author: true,
    },
    orderBy: { publishedAt: 'desc' },
    take: limit,
  })

  return posts
}

export type SearchResult = Prisma.PostGetPayload<{
  include: {
    tags: {
      include: {
        tag: true
      }
    },
    category: true,
    author: true
  }
}>

export async function searchPosts(query: string){
  const searchTerm = query.toLowerCase()
  const posts = await prisma.post.findMany({
    where: {
      published: true,
      OR: [
        { title: { contains: searchTerm, mode: 'insensitive' } },
        { excerpt: { contains: searchTerm, mode: 'insensitive' } },
        { category: { name: { contains: searchTerm, mode: 'insensitive' } } },
        { tags: { some: { tag: { name: { contains: searchTerm, mode: 'insensitive' } } } } },
      ],
    },
    include: {
      tags: { include: { tag: true } },
      category: true,
      author: true,
    },
    orderBy: { publishedAt: 'desc' },
  }) as SearchResult[]

  return posts.map((post) => ({
    ...post,
    coverImage: post.coverImage ?? undefined,
    tags: post.tags.map((t) => t.tag.name),
    category: post.category.name,
    author: {
      name: post.author.name,
      username: post.author.username,
      avatar: post.author.avatar ?? undefined,
    },
    publishedAt: post.publishedAt?.toISOString() ?? '',
    updatedAt: post.updatedAt.toISOString(),
  }))
}

export async function getPostsByCategory(categorySlug: string, page = 1, limit = 6){
  const posts = await prisma.post.findMany({
    where: {
      published: true,
      category: {
        slug: {
          equals: categorySlug,
          mode: 'insensitive', // Case-insensitive match
 },
      },
    },
    include: {
      tags: {
        include: {
          tag: true
        }
      },
      category: true,
      author: true,
    },
    skip: (page - 1) * limit,
    take: limit,
    orderBy: { publishedAt: 'desc' },
  })
  console.log(posts)
  if (!posts || posts.length === 0) {
    return {
      posts: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalPosts: 0,
        hasNext: false,
        hasPrev: false,
      },
    }
  }
  const allPosts = posts.map((post: any) => ({
    ...post,
    publishedAt: post.publishedAt.toISOString(),
    updatedAt: post.updatedAt.toISOString(),
    readingTime: Math.ceil(post.content.split(' ').length / 200), // Assuming average reading speed of 200 wpm
  }))
  console.log('allposts', allPosts)
  const totalPosts = await prisma.post.count({
    where: {
      published: true,
      category: {
        slug: {
          equals: categorySlug,
          mode: 'insensitive', // Case-insensitive match
        },
      },
    },
  })
  console.log('totalPosts', totalPosts)
 const totalPages = Math.ceil(totalPosts / limit)
  console.log('totalPages', totalPages)
  if (totalPages === 0) {
    return {
      posts: [],
      pagination: {
        currentPage: page,
        totalPages: 0,
        totalPosts: 0,
        hasNext: false,
        hasPrev: false,
      },
    }
  }

  return {
    posts: allPosts,
    pagination: {
      currentPage: page,
      totalPages,
      totalPosts: allPosts.length,
      hasNext: page < totalPages,
      hasPrev: page > 1,
    },
  }
}
