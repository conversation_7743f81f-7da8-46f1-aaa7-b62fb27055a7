import prisma  from "./prisma"
import type { Post, Tag, Category, PaginationInfo } from "@/types/blog"
import { Prisma } from "@prisma/client"

  export type TagResult = Prisma.TagGetPayload<{
    include: {
      _count: {
        select: {
          posts: true,
        },
      },
    },
  }>[];

  export type CategoryResult = Prisma.CategoryGetPayload<{
    include: {
      _count: {
        select: {
          posts: true,
        },
      },
    },
  }>[];
  
export class BlogService {
  // Get all published posts with pagination
  static async getPostsPaginated(page = 1, limit = 6) {
    const skip = (page - 1) * limit

    const [posts, totalCount] = await Promise.all([
      prisma.post.findMany({
        where: { published: true },
        include: {
          author: {
            select: { name: true, username: true, avatar: true },
          },
          category: {
            select: { name: true, slug: true, color: true },
          },
          tags: {
            include: {
              tag: {
                select: { name: true, slug: true, color: true },
              },
            },
          },
          _count: {
            select: { likes: true, comments: true },
          },
        },
        orderBy: [{ featured: "desc" }, { publishedAt: "desc" }],
        skip,
        take: limit,
      }),
      prisma.post.count({
        where: { published: true },
      }),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      posts: posts.map(this.transformPost),
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts: totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      } as PaginationInfo,
    }
  }

  // Get post by ID (for admin)
  static async getPostById(id: string) {
    const post = await prisma.post.findUnique({
      where: { id },
      include: {
        author: {
          select: { id: true, name: true, username: true, avatar: true, bio: true },
        },
        category: {
          select: { id: true, name: true, slug: true, color: true },
        },
        tags: {
          include: {
            tag: {
              select: { id: true, name: true, slug: true, color: true },
            },
          },
        },
        comments: {
          where: { status: "APPROVED" },
          include: {
            author: {
              select: { name: true, username: true, avatar: true },
            },
            replies: {
              include: {
                author: {
                  select: { name: true, username: true, avatar: true },
                },
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        seoMeta: true,
        _count: {
          select: { likes: true, comments: true },
        },
      },
    })

    if (!post) return null

    return this.transformPostWithDetails(post)
  }

  // Get post by slug
  static async getPostBySlug(slug: string) {
    const post = await prisma.post.findUnique({
      where: { slug, published: true },
      include: {
        author: {
          select: { name: true, username: true, avatar: true, bio: true },
        },
        category: {
          select: { name: true, slug: true, color: true },
        },
        tags: {
          include: {
            tag: {
              select: { name: true, slug: true, color: true },
            },
          },
        },
        comments: {
          where: { status: "APPROVED" },
          include: {
            author: {
              select: { name: true, username: true, avatar: true },
            },
            replies: {
              include: {
                author: {
                  select: { name: true, username: true, avatar: true },
                },
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        seoMeta: true,
        _count: {
          select: { likes: true, comments: true },
        },
      },
    })

    if (!post) return null

    // Increment view count
    await prisma.post.update({
      where: { id: post.id },
      data: { views: { increment: 1 } },
    })

    // Track analytics
    await prisma.analytics.create({
      data: {
        event: "post_view",
        path: `/blog/${slug}`,
        postId: post.id,
      },
    })

    return this.transformPost(post)
  }

  // Get posts by tag
  static async getPostsByTag(tagSlug: string, page = 1, limit = 6) {
    const skip = (page - 1) * limit

    const [posts, totalCount] = await Promise.all([
      prisma.post.findMany({
        where: {
          published: true,
          tags: {
            some: {
              tag: { slug: tagSlug },
            },
          },
        },
        include: {
          author: {
            select: { name: true, username: true, avatar: true },
          },
          category: {
            select: { name: true, slug: true, color: true },
          },
          tags: {
            include: {
              tag: {
                select: { name: true, slug: true, color: true },
              },
            },
          },
          _count: {
            select: { likes: true, comments: true },
          },
        },
        orderBy: { publishedAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.post.count({
        where: {
          published: true,
          tags: {
            some: {
              tag: { slug: tagSlug },
            },
          },
        },
      }),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      posts: posts.map(this.transformPost),
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts: totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      } as PaginationInfo,
    }
  }

  // Search posts
  static async searchPosts(query: string, page = 1, limit = 6) {
    const skip = (page - 1) * limit

    const [posts, totalCount] = await Promise.all([
      prisma.post.findMany({
        where: {
          published: true,
          OR: [
            { title: { contains: query, mode: "insensitive" } },
            { excerpt: { contains: query, mode: "insensitive" } },
            { content: { contains: query, mode: "insensitive" } },
            {
              tags: {
                some: {
                  tag: {
                    name: { contains: query, mode: "insensitive" },
                  },
                },
              },
            },
            {
              category: {
                name: { contains: query, mode: "insensitive" },
              },
            },
          ],
        },
        include: {
          author: {
            select: { name: true, username: true, avatar: true },
          },
          category: {
            select: { name: true, slug: true, color: true },
          },
          tags: {
            include: {
              tag: {
                select: { name: true, slug: true, color: true },
              },
            },
          },
          _count: {
            select: { likes: true, comments: true },
          },
        },
        orderBy: { publishedAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.post.count({
        where: {
          published: true,
          OR: [
            { title: { contains: query, mode: "insensitive" } },
            { excerpt: { contains: query, mode: "insensitive" } },
            { content: { contains: query, mode: "insensitive" } },
            {
              tags: {
                some: {
                  tag: {
                    name: { contains: query, mode: "insensitive" },
                  },
                },
              },
            },
            {
              category: {
                name: { contains: query, mode: "insensitive" },
              },
            },
          ],
        },
      }),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      posts: posts.map(this.transformPost),
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts: totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      } as PaginationInfo,
    }
  }


  // Get all tags with post counts
  static async getAllTags(){
    const tags = await prisma.tag.findMany({
      include: {
        _count: {
          select: {
            posts: {
              where: {
                post: { published: true },
              },
            },
          },
        },
      },
      orderBy: {
        posts: {
          _count: "desc",
        },
      },
    }) as TagResult

    return tags.map((tag) => ({
      name: tag.name,
      count: tag._count.posts,
    }))
  }



  // Get all categories with post counts
  static async getAllCategories(){
    const categories = await prisma.category.findMany({
      include: {
        _count: {
          select: {
            posts: {
              where: { published: true },
            },
          },
        },
      },
      orderBy: {
        posts: {
          _count: "desc",
        },
      },
    }) as CategoryResult

    return categories.map((category) => ({
      name: category.name,
      count: category._count.posts,
    }))
  }

  // Get featured posts
  static async getFeaturedPosts(limit = 3) {
    const posts = await prisma.post.findMany({
      where: { published: true, featured: true },
      include: {
        author: {
          select: { name: true, username: true, avatar: true },
        },
        category: {
          select: { name: true, slug: true, color: true },
        },
        tags: {
          include: {
            tag: {
              select: { name: true, slug: true, color: true },
            },
          },
        },
        _count: {
          select: { likes: true, comments: true },
        },
      },
      orderBy: { publishedAt: "desc" },
      take: limit,
    })

    return posts.map(this.transformPost)
  }

  // Get related posts
  static async getRelatedPosts(postId: string, limit = 3) {
    const currentPost = await prisma.post.findUnique({
      where: { id: postId },
      include: {
        tags: {
          include: { tag: true },
        },
        category: true,
      },
    })

    if (!currentPost) return []

    const tagIds = currentPost.tags.map((pt) => pt.tag.id)

    const posts = await prisma.post.findMany({
      where: {
        published: true,
        id: { not: postId },
        OR: [
          { categoryId: currentPost.categoryId },
          {
            tags: {
              some: {
                tagId: { in: tagIds },
              },
            },
          },
        ],
      },
      include: {
        author: {
          select: { name: true, username: true, avatar: true },
        },
        category: {
          select: { name: true, slug: true, color: true },
        },
        tags: {
          include: {
            tag: {
              select: { name: true, slug: true, color: true },
            },
          },
        },
        _count: {
          select: { likes: true, comments: true },
        },
      },
      orderBy: { publishedAt: "desc" },
      take: limit,
    })

    return posts.map(this.transformPost)
  }

  // Admin methods
  static async getAllPostsForAdmin(page = 1, limit = 10) {
    const skip = (page - 1) * limit

    const [posts, totalCount] = await Promise.all([
      prisma.post.findMany({
        include: {
          author: {
            select: { name: true, username: true, avatar: true },
          },
          category: {
            select: { name: true, slug: true, color: true },
          },
          tags: {
            include: {
              tag: {
                select: { name: true, slug: true, color: true },
              },
            },
          },
          _count: {
            select: { likes: true, comments: true },
          },
        },
        orderBy: { updatedAt: "desc" },
        skip,
        take: limit,
      }),
      prisma.post.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      posts: posts.map(this.transformPost),
      pagination: {
        currentPage: page,
        totalPages,
        totalPosts: totalCount,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      } as PaginationInfo,
    }
  }

  static async createPost(data: any) {
    const { tags, categoryId, authorId, ...postData } = data

    // 确保分类存在
    const category = await prisma.category.findUnique({
      where: { id: categoryId },
    })

    if (!category) {
      throw new Error("Category not found")
    }

    // 确保作者存在，如果不存在则创建默认作者
    let author = await prisma.user.findUnique({
      where: { id: authorId },
    })

    if (!author) {
      // 创建默认管理员用户
      author = await prisma.user.create({
        data: {
          id: authorId,
          email: "<EMAIL>",
          username: "admin",
          name: "Admin User",
          role: "ADMIN",
        },
      })
    }

    const post = await prisma.post.create({
      data: {
        ...postData,
        authorId: author.id,
        categoryId,
        tags: {
          create:
            tags?.map((tagName: string) => ({
              tag: {
                connectOrCreate: {
                  where: { name: tagName },
                  create: {
                    name: tagName,
                    slug: tagName.toLowerCase().replace(/\s+/g, "-"),
                  },
                },
              },
            })) || [],
        },
      },
      include: {
        author: {
          select: { name: true, username: true, avatar: true },
        },
        category: {
          select: { name: true, slug: true, color: true },
        },
        tags: {
          include: {
            tag: {
              select: { name: true, slug: true, color: true },
            },
          },
        },
        _count: {
          select: { likes: true, comments: true },
        },
      },
    })

    return this.transformPost(post)
  }

  static async updatePost(id: string, data: any) {
    const { tags, categoryId, ...postData } = data

    // First, delete existing tag relations
    await prisma.postTag.deleteMany({
      where: { postId: id },
    })

    const post = await prisma.post.update({
      where: { id },
      data: {
        ...postData,
        categoryId,
        tags: {
          create:
            tags?.map((tagName: string) => ({
              tag: {
                connectOrCreate: {
                  where: { name: tagName },
                  create: {
                    name: tagName,
                    slug: tagName.toLowerCase().replace(/\s+/g, "-"),
                  },
                },
              },
            })) || [],
        },
      },
      include: {
        author: {
          select: { name: true, username: true, avatar: true },
        },
        category: {
          select: { name: true, slug: true, color: true },
        },
        tags: {
          include: {
            tag: {
              select: { name: true, slug: true, color: true },
            },
          },
        },
        _count: {
          select: { likes: true, comments: true },
        },
      },
    })

    return this.transformPost(post)
  }

  static async deletePost(id: string) {
    await prisma.post.delete({
      where: { id },
    })
  }

  // Transform Prisma post to our Post type
  private static transformPost(post: any){
    
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      coverImage: post.coverImage,
      published: post.published,
      featured: post.featured,
      readingTime: post.readingTime,
      views: post.views,
      publishedAt: post.publishedAt?.toISOString() || post.createdAt.toISOString(),
      updatedAt: post.updatedAt.toISOString(),
      author: post.author.name,
      category: post.category.name,
      tags: post.tags.map((pt: any) => pt.tag.name),
      likesCount: post._count?.likes || 0,
      commentsCount: post._count?.comments || 0,
    }
  }

  // Transform with more details for admin
  private static transformPostWithDetails(post: any): any {
    return {
      id: post.id,
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      coverImage: post.coverImage,
      published: post.published,
      featured: post.featured,
      readingTime: post.readingTime,
      views: post.views,
      publishedAt: post.publishedAt?.toISOString() || post.createdAt.toISOString(),
      updatedAt: post.updatedAt.toISOString(),
      createdAt: post.createdAt.toISOString(),
      author: {
        id: post.author.id,
        name: post.author.name,
        username: post.author.username,
        avatar: post.author.avatar,
      },
      category: {
        id: post.category.id,
        name: post.category.name,
        slug: post.category.slug,
      },
      tags: post.tags.map((pt: any) => ({
        id: pt.tag.id,
        name: pt.tag.name,
        slug: pt.tag.slug,
      })),
      likesCount: post._count?.likes || 0,
      commentsCount: post._count?.comments || 0,
    }
  }
}
